#Health insurance plan predictor
An interactive machine learning-based Insurance Planner with a user-friendly UI. Helps users explore the best insurance options based on personal features like age, income, dependents, and health indicators.
#working description:
This project is an ML-powered web application that helps users identify the most suitable insurance plans based on key features such as:

- Age
- Annual income
- Number of dependents
- Health condition (i.e, BMI, smoking status)
- Employment type

The application includes:
- A trained ML model for prediction or recommendation
- A clean, user-friendly web interface
- Insights and explanations for better decision-making
#Tech stack
  -HTML,CSS,BOOTSTRAP
  -Typescript,json
  -Machine learning algorithms
  -Python
  -clerk for user signin/signup authentication
